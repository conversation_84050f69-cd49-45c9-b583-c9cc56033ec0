#!/usr/bin/env python3

import gi
gi.require_version('Gst', '1.0')
from gi.repository import Gst, GLib
import sys

def test_hls_stream():
    """Test HLS stream with minimal playbin setup"""
    
    # Initialize GStreamer
    Gst.init(None)
    
    # Create pipeline with playbin
    pipeline = Gst.Pipeline.new("test-pipeline")
    playbin = Gst.ElementFactory.make("playbin", "player")
    
    if not playbin:
        print("Failed to create playbin")
        return False
    
    # Add playbin to pipeline
    pipeline.add(playbin)
    
    # Set URI
    playbin.set_property("uri", 
        "https://admin-srs.volks-dev.knizsoft.com/live/3c0c5818-03b3-4959-baae-8b8817d865ba.m3u8")
    
    # Create main loop
    loop = GLib.MainLoop()
    
    def on_message(bus, message):
        t = message.type
        if t == Gst.MessageType.EOS:
            print("End of stream")
            loop.quit()
        elif t == Gst.MessageType.ERROR:
            err, debug = message.parse_error()
            print(f"Error: {err}")
            print(f"Debug: {debug}")
            print(f"Source: {message.src.get_name()}")
            loop.quit()
        elif t == Gst.MessageType.WARNING:
            warn, debug = message.parse_warning()
            print(f"Warning: {warn}")
            print(f"Debug: {debug}")
        elif t == Gst.MessageType.STATE_CHANGED:
            if message.src == pipeline:
                old, new, pending = message.parse_state_changed()
                print(f"State changed: {old.value_nick} -> {new.value_nick}")
        elif t == Gst.MessageType.BUFFERING:
            percent = message.parse_buffering()
            print(f"Buffering: {percent}%")
    
    # Connect bus
    bus = pipeline.get_bus()
    bus.add_signal_watch()
    bus.connect("message", on_message)
    
    # Start pipeline
    print("Starting test pipeline...")
    ret = pipeline.set_state(Gst.State.PLAYING)
    
    if ret == Gst.StateChangeReturn.FAILURE:
        print("Failed to start pipeline")
        return False
    
    print("Pipeline started. Waiting for 10 seconds...")
    
    # Run for 10 seconds
    try:
        GLib.timeout_add_seconds(10, lambda: loop.quit())
        loop.run()
    except KeyboardInterrupt:
        print("Interrupted")
    
    # Cleanup
    pipeline.set_state(Gst.State.NULL)
    print("Test completed")
    return True

if __name__ == "__main__":
    test_hls_stream()
