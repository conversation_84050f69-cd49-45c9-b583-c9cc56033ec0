#!/usr/bin/env python3

import gi
gi.require_version('Gst', '1.0')
from gi.repository import Gst, GLib
import sys
import signal

class HLSPipeline:
    def __init__(self):
        # Initialize GStreamer
        Gst.init(None)
        
        # Create pipeline
        self.pipeline = Gst.Pipeline.new("hls-streaming-pipeline")
        
        # Create elements
        self.create_elements()
        
        # Add elements to pipeline
        self.add_elements_to_pipeline()
        
        # Link elements
        self.link_elements()
        
        # Connect signals
        self.connect_signals()
        
        # Create main loop
        self.loop = GLib.MainLoop()
        
    def create_elements(self):
        """Create all GStreamer elements"""
        # Use playbin for maximum compatibility
        self.playbin = Gst.ElementFactory.make("playbin", "player")

        # Check if playbin was created successfully
        if not self.playbin:
            print("Failed to create playbin element")
            sys.exit(1)
    
    def set_element_properties(self):
        """Set properties for elements"""
        # Try multiple HLS URLs in case the original doesn't work
        hls_urls = [
            "https://admin-srs.volks-dev.knizsoft.com/live/3c0c5818-03b3-4959-baae-8b8817d865ba.m3u8",
            "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8",  # Test stream
            "https://demo.unified-streaming.com/k8s/features/stable/video/tears-of-steel/tears-of-steel.ism/.m3u8"  # Another test stream
        ]

        # Set URI for playbin (using first URL for now)
        self.playbin.set_property("uri", hls_urls[0])
        print(f"Using HLS URL: {hls_urls[0]}")

        # Create and set video sink
        self.nveglglessink = Gst.ElementFactory.make("nveglglessink", "sink")
        if self.nveglglessink:
            self.playbin.set_property("video-sink", self.nveglglessink)
        else:
            print("Warning: Could not create nveglglessink, using default video sink")

        # Set additional playbin properties for better streaming
        self.playbin.set_property("buffer-duration", 5000000000)  # 5 seconds
        self.playbin.set_property("buffer-size", 2097152)  # 2MB
    
    def add_elements_to_pipeline(self):
        """Add all elements to the pipeline"""
        # Playbin is self-contained, just add it to the pipeline
        self.pipeline.add(self.playbin)
    
    def link_elements(self):
        """Link elements together"""
        # Playbin handles all linking internally
        pass
    
    def connect_signals(self):
        """Connect signals"""
        # Connect bus messages
        bus = self.pipeline.get_bus()
        bus.add_signal_watch()
        bus.connect("message", self.on_bus_message)

    def on_bus_message(self, bus, message):
        """Handle bus messages"""
        t = message.type

        if t == Gst.MessageType.EOS:
            print("End of stream")
            self.loop.quit()
        elif t == Gst.MessageType.ERROR:
            err, debug = message.parse_error()
            print(f"Error: {err}, Debug: {debug}")
            print(f"Error source: {message.src.get_name()}")
            self.loop.quit()
        elif t == Gst.MessageType.WARNING:
            warn, debug = message.parse_warning()
            print(f"Warning: {warn}, Debug: {debug}")
            print(f"Warning source: {message.src.get_name()}")
        elif t == Gst.MessageType.STATE_CHANGED:
            if message.src == self.pipeline:
                old_state, new_state, pending_state = message.parse_state_changed()
                print(f"Pipeline state changed from {old_state.value_nick} to {new_state.value_nick}")
        elif t == Gst.MessageType.BUFFERING:
            percent = message.parse_buffering()
            print(f"Buffering: {percent}%")
        elif t == Gst.MessageType.STREAM_STATUS:
            status, owner = message.parse_stream_status()
            print(f"Stream status: {status} from {owner.get_name()}")
    
    def run(self):
        """Start the pipeline and main loop"""
        print("Starting pipeline...")
        
        # Set element properties
        self.set_element_properties()
        
        # Set pipeline to playing state
        ret = self.pipeline.set_state(Gst.State.PLAYING)
        if ret == Gst.StateChangeReturn.FAILURE:
            print("Unable to set the pipeline to playing state")
            return
        
        print("Pipeline started. Press Ctrl+C to stop.")
        
        try:
            # Run the main loop
            self.loop.run()
        except KeyboardInterrupt:
            print("\nInterrupted by user")
        finally:
            # Clean up
            self.cleanup()
    
    def cleanup(self):
        """Clean up resources"""
        print("Cleaning up...")
        self.pipeline.set_state(Gst.State.NULL)
        print("Pipeline stopped")

def signal_handler(sig, frame):
    """Handle Ctrl+C"""
    print("\nReceived interrupt signal")
    sys.exit(0)

def main():
    """Main function"""
    # Register signal handler
    signal.signal(signal.SIGINT, signal_handler)

    # Create and run pipeline
    pipeline = HLSPipeline()
    pipeline.run()

if __name__ == "__main__":
    main()