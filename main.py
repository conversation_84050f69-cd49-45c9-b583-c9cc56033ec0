#!/usr/bin/env python3

import gi
gi.require_version('Gst', '1.0')
from gi.repository import Gst, GLib
import sys
import signal

class HLSPipeline:
    def __init__(self):
        # Initialize GStreamer
        Gst.init(None)
        
        # Create pipeline
        self.pipeline = Gst.Pipeline.new("hls-streaming-pipeline")
        
        # Create elements
        self.create_elements()
        
        # Add elements to pipeline
        self.add_elements_to_pipeline()
        
        # Link elements
        self.link_elements()
        
        # Connect signals
        self.connect_signals()
        
        # Create main loop
        self.loop = GLib.MainLoop()
        
    def create_elements(self):
        """Create all GStreamer elements"""
        # URI decode bin - handles HLS automatically
        self.uridecodebin = Gst.ElementFactory.make("uridecodebin", "source")

        # Queue for decoded video
        self.queue = Gst.ElementFactory.make("queue", "queue")

        # Video converter (for format conversion)
        self.nvvideoconvert = Gst.ElementFactory.make("nvvideoconvert", "converter")

        # Sink
        self.nveglglessink = Gst.ElementFactory.make("nveglglessink", "sink")

        # Check if all elements were created successfully
        elements = [
            ("uridecodebin", self.uridecodebin),
            ("queue", self.queue),
            ("nvvideoconvert", self.nvvideoconvert),
            ("nveglglessink", self.nveglglessink)
        ]

        for name, element in elements:
            if not element:
                print(f"Failed to create {name} element")
                sys.exit(1)
    
    def set_element_properties(self):
        """Set properties for elements"""
        # Set URI for uridecodebin
        self.uridecodebin.set_property("uri",
            "https://admin-srs.volks-dev.knizsoft.com/live/3c0c5818-03b3-4959-baae-8b8817d865ba.m3u8")
    
    def add_elements_to_pipeline(self):
        """Add all elements to the pipeline"""
        elements = [
            self.uridecodebin, self.queue, self.nvvideoconvert, self.nveglglessink
        ]

        for element in elements:
            self.pipeline.add(element)
    
    def link_elements(self):
        """Link elements together"""
        # Link static elements
        elements_to_link = [
            (self.queue, self.nvvideoconvert),
            (self.nvvideoconvert, self.nveglglessink)
        ]

        for src, dst in elements_to_link:
            if not src.link(dst):
                print(f"Failed to link {src.get_name()} to {dst.get_name()}")
                sys.exit(1)
    
    def connect_signals(self):
        """Connect dynamic pad signals"""
        # Connect uridecodebin pad-added signal
        self.uridecodebin.connect("pad-added", self.on_uridecodebin_pad_added)

        # Connect bus messages
        bus = self.pipeline.get_bus()
        bus.add_signal_watch()
        bus.connect("message", self.on_bus_message)
    
    def on_hlsdemux_pad_added(self, demux, pad):
        """Handle dynamic pad from hlsdemux"""
        # Get sink pad from queue1
        sink_pad = self.queue1.get_static_pad("sink")
        
        if not sink_pad.is_linked():
            # Link the pad
            result = pad.link(sink_pad)
            if result != Gst.PadLinkReturn.OK:
                print("Failed to link hlsdemux pad")
    
    def on_tsdemux_pad_added(self, demux, pad):
        """Handle dynamic pad from tsdemux"""
        # Check if this is a video pad
        caps = pad.get_current_caps()
        if caps:
            structure = caps.get_structure(0)
            if structure and structure.get_name().startswith("video/"):
                # Get sink pad from h264parse
                sink_pad = self.h264parse.get_static_pad("sink")
                
                if not sink_pad.is_linked():
                    # Link the pad
                    result = pad.link(sink_pad)
                    if result != Gst.PadLinkReturn.OK:
                        print("Failed to link tsdemux video pad")
    
    def on_bus_message(self, bus, message):
        """Handle bus messages"""
        t = message.type
        
        if t == Gst.MessageType.EOS:
            print("End of stream")
            self.loop.quit()
        elif t == Gst.MessageType.ERROR:
            err, debug = message.parse_error()
            print(f"Error: {err}, Debug: {debug}")
            self.loop.quit()
        elif t == Gst.MessageType.WARNING:
            warn, debug = message.parse_warning()
            print(f"Warning: {warn}, Debug: {debug}")
        elif t == Gst.MessageType.STATE_CHANGED:
            if message.src == self.pipeline:
                old_state, new_state, pending_state = message.parse_state_changed()
                print(f"Pipeline state changed from {old_state.value_nick} to {new_state.value_nick}")
    
    def run(self):
        """Start the pipeline and main loop"""
        print("Starting pipeline...")
        
        # Set element properties
        self.set_element_properties()
        
        # Set pipeline to playing state
        ret = self.pipeline.set_state(Gst.State.PLAYING)
        if ret == Gst.StateChangeReturn.FAILURE:
            print("Unable to set the pipeline to playing state")
            return
        
        print("Pipeline started. Press Ctrl+C to stop.")
        
        try:
            # Run the main loop
            self.loop.run()
        except KeyboardInterrupt:
            print("\nInterrupted by user")
        finally:
            # Clean up
            self.cleanup()
    
    def cleanup(self):
        """Clean up resources"""
        print("Cleaning up...")
        self.pipeline.set_state(Gst.State.NULL)
        print("Pipeline stopped")

def signal_handler(sig, frame):
    """Handle Ctrl+C"""
    print("\nReceived interrupt signal")
    sys.exit(0)

def main():
    """Main function"""
    # Register signal handler
    signal.signal(signal.SIGINT, signal_handler)

    # Create and run pipeline
    pipeline = HLSPipeline()
    pipeline.run()

if __name__ == "__main__":
    main()