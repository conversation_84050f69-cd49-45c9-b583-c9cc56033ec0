#!/usr/bin/env python3

import gi
gi.require_version('Gst', '1.0')
from gi.repository import Gst, GLib
import sys
import signal

class HLSPipeline:
    def __init__(self):
        # Initialize GStreamer
        Gst.init(None)
        
        # Create pipeline
        self.pipeline = Gst.Pipeline.new("hls-streaming-pipeline")
        
        # Create elements
        self.create_elements()
        
        # Add elements to pipeline
        self.add_elements_to_pipeline()
        
        # Link elements
        self.link_elements()
        
        # Connect signals
        self.connect_signals()
        
        # Create main loop
        self.loop = GLib.MainLoop()
        
    def create_elements(self):
        """Create all GStreamer elements"""
        # Source
        self.souphttpsrc = Gst.ElementFactory.make("souphttpsrc", "source")

        # HLS demuxer (using standard hlsdemux)
        self.hlsdemux = Gst.ElementFactory.make("hlsdemux", "demux")

        # Queues
        self.queue1 = Gst.ElementFactory.make("queue", "queue1")
        self.queue2 = Gst.ElementFactory.make("queue", "queue2")

        # Transport stream demuxer
        self.tsdemux = Gst.ElementFactory.make("tsdemux", "tsdemux")

        # H264 parser
        self.h264parse = Gst.ElementFactory.make("h264parse", "h264parse")

        # NVIDIA decoder
        self.nvv4l2decoder = Gst.ElementFactory.make("nvv4l2decoder", "decoder")

        # Video converter (for format conversion)
        self.nvvideoconvert = Gst.ElementFactory.make("nvvideoconvert", "converter")

        # Sink
        self.nveglglessink = Gst.ElementFactory.make("nveglglessink", "sink")

        # Check if all elements were created successfully
        elements = [
            ("souphttpsrc", self.souphttpsrc),
            ("hlsdemux", self.hlsdemux),
            ("queue", self.queue1),
            ("queue", self.queue2),
            ("tsdemux", self.tsdemux),
            ("h264parse", self.h264parse),
            ("nvv4l2decoder", self.nvv4l2decoder),
            ("nvvideoconvert", self.nvvideoconvert),
            ("nveglglessink", self.nveglglessink)
        ]

        for name, element in elements:
            if not element:
                print(f"Failed to create {name} element")
                sys.exit(1)
    
    def set_element_properties(self):
        """Set properties for elements"""
        # Set source location
        hls_url = "https://admin-srs.volks-dev.knizsoft.com/live/3c0c5818-03b3-4959-baae-8b8817d865ba.m3u8"
        self.souphttpsrc.set_property("location", hls_url)
        print(f"Using HLS URL: {hls_url}")

        # Set souphttpsrc properties for better streaming
        self.souphttpsrc.set_property("is-live", True)
        self.souphttpsrc.set_property("timeout", 30)  # 30 second timeout
        self.souphttpsrc.set_property("retries", 3)   # Retry 3 times on failure

        # Additional souphttpsrc properties for better HLS handling
        self.souphttpsrc.set_property("user-agent", "GStreamer HLS Player")
        self.souphttpsrc.set_property("keep-alive", True)

        # Set hlsdemux properties for better handling
        # Note: hlsdemux has fewer configurable properties than hlsdemux2
        # But we can still set some basic properties if available
        try:
            # These properties may or may not be available depending on GStreamer version
            self.hlsdemux.set_property("connection-speed", 1000)
        except:
            print("Note: Some hlsdemux properties not available in this GStreamer version")
    
    def add_elements_to_pipeline(self):
        """Add all elements to the pipeline"""
        elements = [
            self.souphttpsrc, self.hlsdemux, self.queue1, self.tsdemux,
            self.h264parse, self.nvv4l2decoder, self.queue2,
            self.nvvideoconvert, self.nveglglessink
        ]

        for element in elements:
            self.pipeline.add(element)
    
    def link_elements(self):
        """Link elements together"""
        # Link static elements first
        if not self.souphttpsrc.link(self.hlsdemux):
            print("Failed to link souphttpsrc to hlsdemux")
            sys.exit(1)

        # Link the rest of the static pipeline
        elements_to_link = [
            (self.queue1, self.tsdemux),
            (self.h264parse, self.nvv4l2decoder),
            (self.nvvideoconvert, self.nveglglessink)
        ]

        for src, dst in elements_to_link:
            if not src.link(dst):
                print(f"Failed to link {src.get_name()} to {dst.get_name()}")
                sys.exit(1)

        # Create caps for decoder output
        caps = Gst.Caps.from_string("video/x-raw(memory:NVMM), format=NV12")

        # Link decoder to queue2 with caps
        if not self.nvv4l2decoder.link_filtered(self.queue2, caps):
            print("Failed to link decoder to queue2 with caps")
            sys.exit(1)

        # Link queue2 to nvvideoconvert
        if not self.queue2.link(self.nvvideoconvert):
            print("Failed to link queue2 to nvvideoconvert")
            sys.exit(1)
    
    def connect_signals(self):
        """Connect dynamic pad signals"""
        # Connect hlsdemux pad-added signal
        self.hlsdemux.connect("pad-added", self.on_hlsdemux_pad_added)

        # Connect tsdemux pad-added signal
        self.tsdemux.connect("pad-added", self.on_tsdemux_pad_added)

        # Connect bus messages
        bus = self.pipeline.get_bus()
        bus.add_signal_watch()
        bus.connect("message", self.on_bus_message)

    def on_hlsdemux_pad_added(self, demux, pad):
        """Handle dynamic pad from hlsdemux"""
        # Get sink pad from queue1
        sink_pad = self.queue1.get_static_pad("sink")

        if not sink_pad.is_linked():
            # Link the pad
            result = pad.link(sink_pad)
            if result != Gst.PadLinkReturn.OK:
                print("Failed to link hlsdemux pad")
            else:
                print("Successfully linked hlsdemux pad")

    def on_tsdemux_pad_added(self, demux, pad):
        """Handle dynamic pad from tsdemux"""
        # Check if this is a video pad
        caps = pad.get_current_caps()
        if caps:
            structure = caps.get_structure(0)
            if structure and structure.get_name().startswith("video/"):
                # Get sink pad from h264parse
                sink_pad = self.h264parse.get_static_pad("sink")

                if not sink_pad.is_linked():
                    # Link the pad
                    result = pad.link(sink_pad)
                    if result != Gst.PadLinkReturn.OK:
                        print("Failed to link tsdemux video pad")
                    else:
                        print("Successfully linked tsdemux video pad")

    def on_bus_message(self, bus, message):
        """Handle bus messages"""
        t = message.type

        if t == Gst.MessageType.EOS:
            print("End of stream")
            self.loop.quit()
        elif t == Gst.MessageType.ERROR:
            err, debug = message.parse_error()
            print(f"Error: {err}, Debug: {debug}")
            print(f"Error source: {message.src.get_name()}")
            self.loop.quit()
        elif t == Gst.MessageType.WARNING:
            warn, debug = message.parse_warning()
            print(f"Warning: {warn}, Debug: {debug}")
            print(f"Warning source: {message.src.get_name()}")
        elif t == Gst.MessageType.STATE_CHANGED:
            if message.src == self.pipeline:
                old_state, new_state, pending_state = message.parse_state_changed()
                print(f"Pipeline state changed from {old_state.value_nick} to {new_state.value_nick}")
        elif t == Gst.MessageType.BUFFERING:
            percent = message.parse_buffering()
            print(f"Buffering: {percent}%")
        elif t == Gst.MessageType.STREAM_STATUS:
            status, owner = message.parse_stream_status()
            print(f"Stream status: {status} from {owner.get_name()}")
    
    def run(self):
        """Start the pipeline and main loop"""
        print("Starting pipeline...")
        
        # Set element properties
        self.set_element_properties()
        
        # Set pipeline to playing state
        ret = self.pipeline.set_state(Gst.State.PLAYING)
        if ret == Gst.StateChangeReturn.FAILURE:
            print("Unable to set the pipeline to playing state")
            return
        
        print("Pipeline started. Press Ctrl+C to stop.")
        
        try:
            # Run the main loop
            self.loop.run()
        except KeyboardInterrupt:
            print("\nInterrupted by user")
        finally:
            # Clean up
            self.cleanup()
    
    def cleanup(self):
        """Clean up resources"""
        print("Cleaning up...")
        self.pipeline.set_state(Gst.State.NULL)
        print("Pipeline stopped")

def signal_handler(sig, frame):
    """Handle Ctrl+C"""
    print("\nReceived interrupt signal")
    sys.exit(0)

def main():
    """Main function"""
    # Register signal handler
    signal.signal(signal.SIGINT, signal_handler)

    # Create and run pipeline
    pipeline = HLSPipeline()
    pipeline.run()

if __name__ == "__main__":
    main()